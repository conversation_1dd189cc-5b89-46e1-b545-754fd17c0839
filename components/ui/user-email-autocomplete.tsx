"use client"

import { useState, useEffect, useRef, useMemo } from "react"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { X, Mail, Users } from "lucide-react"
import { cn } from "@/lib/utils"

interface UserEmailAutocompleteProps {
  value: string[]
  onChange: (emails: string[]) => void
  placeholder?: string
  suggestions?: string[]
  maxEmails?: number
  className?: string
  disabled?: boolean
}

export function UserEmailAutocomplete({
  value = [],
  onChange,
  placeholder = "Enter email addresses...",
  suggestions = [],
  maxEmails = 10,
  className,
  disabled = false,
}: UserEmailAutocompleteProps) {
  const [inputValue, setInputValue] = useState("")
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [focusedSuggestionIndex, setFocusedSuggestionIndex] = useState(-1)
  const inputRef = useRef<HTMLInputElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Filter suggestions based on input value and exclude already selected emails
  const filteredSuggestions = useMemo(() => {
    if (!inputValue || inputValue.length < 2) return []
    
    return suggestions
      .filter((email) => 
        email.toLowerCase().includes(inputValue.toLowerCase()) &&
        !value.includes(email)
      )
      .slice(0, 5) // Limit to 5 suggestions
  }, [inputValue, suggestions, value])

  // Email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setInputValue(newValue)
    setShowSuggestions(newValue.length >= 2)
    setFocusedSuggestionIndex(-1)
  }

  // Handle input key down
  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" || e.key === "Tab" || e.key === ",") {
      e.preventDefault()
      addEmail(inputValue.trim())
    } else if (e.key === "Backspace" && inputValue === "" && value.length > 0) {
      // Remove last email if input is empty
      removeEmail(value[value.length - 1])
    } else if (e.key === "ArrowDown") {
      e.preventDefault()
      setFocusedSuggestionIndex((prev) => 
        prev < filteredSuggestions.length - 1 ? prev + 1 : 0
      )
    } else if (e.key === "ArrowUp") {
      e.preventDefault()
      setFocusedSuggestionIndex((prev) => 
        prev > 0 ? prev - 1 : filteredSuggestions.length - 1
      )
    } else if (e.key === "Enter" && focusedSuggestionIndex >= 0) {
      e.preventDefault()
      addEmail(filteredSuggestions[focusedSuggestionIndex])
    } else if (e.key === "Escape") {
      setShowSuggestions(false)
      setFocusedSuggestionIndex(-1)
    }
  }

  // Add email to the list
  const addEmail = (email: string) => {
    const trimmedEmail = email.trim().toLowerCase()
    
    if (!trimmedEmail) return
    
    if (!emailRegex.test(trimmedEmail)) {
      // Could show error toast here
      return
    }
    
    if (value.includes(trimmedEmail)) {
      // Email already added
      setInputValue("")
      setShowSuggestions(false)
      return
    }
    
    if (value.length >= maxEmails) {
      // Max emails reached
      return
    }
    
    onChange([...value, trimmedEmail])
    setInputValue("")
    setShowSuggestions(false)
    setFocusedSuggestionIndex(-1)
  }

  // Remove email from the list
  const removeEmail = (emailToRemove: string) => {
    onChange(value.filter((email) => email !== emailToRemove))
  }

  // Handle suggestion click
  const handleSuggestionClick = (email: string) => {
    addEmail(email)
    inputRef.current?.focus()
  }

  // Handle input blur
  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => {
      if (!containerRef.current?.contains(document.activeElement)) {
        setShowSuggestions(false)
        setFocusedSuggestionIndex(-1)
        
        // Add current input value if it's a valid email
        if (inputValue.trim() && emailRegex.test(inputValue.trim())) {
          addEmail(inputValue.trim())
        }
      }
    }, 150)
  }

  // Handle input focus
  const handleInputFocus = () => {
    if (inputValue.length >= 2) {
      setShowSuggestions(true)
    }
  }

  return (
    <div className={cn("relative", className)} ref={containerRef}>
      <div className="flex flex-wrap gap-2 p-3 border rounded-md bg-background min-h-[42px] focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2">
        {/* Selected emails */}
        {value.map((email) => (
          <Badge
            key={email}
            variant="secondary"
            className="flex items-center gap-1 px-2 py-1"
          >
            <Mail className="h-3 w-3" />
            <span className="text-xs">{email}</span>
            {!disabled && (
              <button
                type="button"
                onClick={() => removeEmail(email)}
                className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            )}
          </Badge>
        ))}
        
        {/* Input field */}
        {(!disabled && value.length < maxEmails) && (
          <input
            ref={inputRef}
            type="email"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleInputKeyDown}
            onBlur={handleInputBlur}
            onFocus={handleInputFocus}
            placeholder={value.length === 0 ? placeholder : ""}
            className="flex-1 min-w-[120px] bg-transparent border-none outline-none text-sm placeholder:text-muted-foreground"
            disabled={disabled}
          />
        )}
      </div>

      {/* Suggestions dropdown */}
      {showSuggestions && filteredSuggestions.length > 0 && (
        <div className="absolute z-50 mt-1 w-full bg-popover border rounded-md shadow-lg max-h-60 overflow-auto">
          <div className="p-2">
            <div className="flex items-center gap-2 text-xs text-muted-foreground mb-2">
              <Users className="h-3 w-3" />
              <span>Suggestions from your squads</span>
            </div>
            {filteredSuggestions.map((email, index) => (
              <div
                key={email}
                className={cn(
                  "flex items-center gap-2 px-3 py-2 rounded-sm cursor-pointer text-sm",
                  index === focusedSuggestionIndex
                    ? "bg-accent text-accent-foreground"
                    : "hover:bg-accent hover:text-accent-foreground"
                )}
                onClick={() => handleSuggestionClick(email)}
                onMouseEnter={() => setFocusedSuggestionIndex(index)}
              >
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span>{email}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Helper text */}
      {value.length > 0 && (
        <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
          <span>{value.length} email{value.length !== 1 ? 's' : ''} added</span>
          {maxEmails && (
            <span>{value.length}/{maxEmails}</span>
          )}
        </div>
      )}
    </div>
  )
}
