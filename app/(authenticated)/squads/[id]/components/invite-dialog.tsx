"use client"

import { useState, useEffect, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Mail, Link2, Copy, Check, Users, Clock, Send, ExternalLink } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { UserEmailAutocomplete } from "@/components/ui/user-email-autocomplete"
import {
  useGenerateInvitationLink,
  useInvitationLink,
  useSendInvitationEmails,
  useInvitationSends,
  useRealtimeSquadInvitationSends,
} from "@/lib/domains/invitation/invitation.hooks"
import { useUserSquads } from "@/lib/domains/squad/squad.hooks"
import { generateInvitationLink } from "@/lib/email-service"

interface InviteDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  squad: { id: string; name: string; members: string[] }
  currentUser: { uid: string; email?: string; displayName?: string }
  onInviteSent?: () => void
}

export function InviteDialog({
  open,
  onOpenChange,
  squad,
  currentUser,
  onInviteSent,
}: InviteDialogProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("link")
  const [selectedEmails, setSelectedEmails] = useState<string[]>([])
  const [copiedLink, setCopiedLink] = useState(false)

  // Hooks for invitation link management
  const { invitationLink, loading: linkLoading } = useInvitationLink(squad.id, true)
  const { generate: generateLink, generating } = useGenerateInvitationLink()
  const { sendEmails, sending } = useSendInvitationEmails()

  // Get invitation sends for this squad (real-time)
  const { invitationSends } = useRealtimeSquadInvitationSends(squad.id)

  // Get user squads to extract email suggestions
  const { squads } = useUserSquads(false)

  // Generate email suggestions from squad members
  const emailSuggestions = useMemo(() => {
    const allEmails = new Set<string>()

    // Add emails from all squads the user is part of
    squads.forEach((squadItem) => {
      // This would need to be enhanced to get actual member emails
      // For now, we'll use a placeholder approach
    })

    return Array.from(allEmails)
  }, [squads])

  // Generate or get invitation link
  const handleGenerateLink = async () => {
    if (invitationLink) return invitationLink

    const newLink = await generateLink(squad.id, squad.name)
    if (newLink) {
      toast({
        title: "Invitation link generated",
        description: "Your shareable invitation link is ready!",
      })
    }
    return newLink
  }

  // Copy invitation link to clipboard
  const handleCopyLink = async () => {
    const link = invitationLink || (await handleGenerateLink())
    if (!link) return

    const fullLink = generateInvitationLink(link.id)

    try {
      await navigator.clipboard.writeText(fullLink)
      setCopiedLink(true)
      toast({
        title: "Link copied",
        description: "Invitation link copied to clipboard",
      })

      setTimeout(() => setCopiedLink(false), 2000)
    } catch (error) {
      toast({
        title: "Failed to copy",
        description: "Please copy the link manually",
        variant: "destructive",
      })
    }
  }

  // Send email invitations
  const handleSendEmails = async () => {
    if (selectedEmails.length === 0) return

    const link = invitationLink || (await handleGenerateLink())
    if (!link) {
      toast({
        title: "Error",
        description: "Failed to generate invitation link",
        variant: "destructive",
      })
      return
    }

    const result = await sendEmails(squad.id, link.id, selectedEmails)

    if (result.success) {
      const successCount = result.results?.filter((r) => r.success).length || 0
      const failCount = (result.results?.length || 0) - successCount

      toast({
        title: "Invitations sent",
        description: `${successCount} invitation${successCount !== 1 ? "s" : ""} sent successfully${failCount > 0 ? `, ${failCount} failed` : ""}`,
      })

      setSelectedEmails([])
      if (onInviteSent) onInviteSent()
    } else {
      toast({
        title: "Failed to send invitations",
        description: result.error || "Please try again",
        variant: "destructive",
      })
    }
  }

  // Format expiration date
  const formatExpirationDate = (expiresAt: any) => {
    if (!expiresAt) return ""
    const date = expiresAt.toDate ? expiresAt.toDate() : new Date(expiresAt)
    return date.toLocaleDateString()
  }

  // Get recent invitation sends for display
  const recentSends = useMemo(() => {
    if (!invitationLink || !invitationSends) return []

    return invitationSends
      .filter((send) => send.invitationId === invitationLink.id)
      .sort((a, b) => {
        const aDate = a.sentAt?.toDate ? a.sentAt.toDate() : new Date(a.sentAt)
        const bDate = b.sentAt?.toDate ? b.sentAt.toDate() : new Date(b.sentAt)
        return bDate.getTime() - aDate.getTime()
      })
      .slice(0, 5) // Show last 5 sends
  }, [invitationLink, invitationSends])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Invite to {squad.name}</DialogTitle>
          <DialogDescription>
            Share an invitation link or send individual email invitations to grow your squad.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="link" className="flex items-center gap-2">
              <Link2 className="h-4 w-4" />
              Shareable Link
            </TabsTrigger>
            <TabsTrigger value="email" className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Email Invitations
            </TabsTrigger>
          </TabsList>

          <TabsContent value="link" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="h-4 w-4" />
                <span>
                  {invitationLink
                    ? `Expires on ${formatExpirationDate(invitationLink.expiresAt)}`
                    : "Links expire after 10 days"}
                </span>
              </div>

              {invitationLink ? (
                <div className="space-y-3">
                  <div className="flex items-center gap-2 p-3 bg-muted rounded-md">
                    <Input
                      value={generateInvitationLink(invitationLink.id)}
                      readOnly
                      className="flex-1 bg-background"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopyLink}
                      disabled={linkLoading}
                    >
                      {copiedLink ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>

                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Users className="h-4 w-4" />
                    <span>Anyone with this link can join your squad</span>
                  </div>
                </div>
              ) : (
                <div className="text-center py-6">
                  <Button
                    onClick={handleGenerateLink}
                    disabled={generating || linkLoading}
                    className="flex items-center gap-2"
                  >
                    <Link2 className="h-4 w-4" />
                    {generating ? "Generating..." : "Generate Invitation Link"}
                  </Button>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="email" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="emails">Email Addresses</Label>
                <UserEmailAutocomplete
                  value={selectedEmails}
                  onChange={setSelectedEmails}
                  suggestions={emailSuggestions}
                  placeholder="Enter email addresses..."
                  maxEmails={10}
                  className="mt-2"
                />
              </div>

              {recentSends.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Recent Invitations</Label>
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {recentSends.map((send) => (
                      <div
                        key={send.id}
                        className="flex items-center justify-between p-2 bg-muted rounded-sm text-sm"
                      >
                        <span className="flex items-center gap-2">
                          <Mail className="h-3 w-3" />
                          {send.email}
                        </span>
                        <Badge variant="secondary" className="text-xs">
                          {send.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex items-center justify-between">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>

          {activeTab === "email" && (
            <Button
              onClick={handleSendEmails}
              disabled={sending || selectedEmails.length === 0}
              className="flex items-center gap-2"
            >
              <Send className="h-4 w-4" />
              {sending
                ? "Sending..."
                : `Send ${selectedEmails.length} Invitation${selectedEmails.length !== 1 ? "s" : ""}`}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
