"use client"

import { useEffect, useState, useMemo } from "react"
import { useInvitationStore } from "./invitation.store"
import {
  Invitation,
  InvitationCreateData,
  InvitationStatus,
  InvitationLink,
  InvitationLinkCreateData,
  InvitationSend,
  InvitationSendCreateData,
} from "./invitation.types"
import { useUser, useUserData } from "@/lib/domains/auth/auth.hooks"
import { generateInvitationLink, sendInvitationEmail } from "@/lib/email-service"
import { SquadService } from "../squad/squad.service"
import { UserService } from "../user/user.service"
import { InvitationService } from "./invitation.service"
import { InvitationSendService } from "./invitation-send.service"

// Export real-time hooks
export * from "./invitation.realtime.hooks"

/**
 * Hook to get all invitations for a squad
 */
export const useSquadInvitations = (squadId: string, useRealtime: boolean = false) => {
  // If useRealtime is true, use the real-time hook instead
  if (useRealtime) {
    // Import the hook dynamically to avoid circular dependencies
    const { useRealtimeSquadInvitations } = require("./invitation.realtime.hooks")
    return useRealtimeSquadInvitations(squadId)
  }

  // Otherwise use the regular store
  const { invitations, loading, error, fetchSquadInvitations } = useInvitationStore()

  useEffect(() => {
    if (squadId) {
      fetchSquadInvitations(squadId)
    }

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [squadId, fetchSquadInvitations])

  return { invitations, loading, error }
}

/**
 * Hook to get all invitations for the current user
 */
export const useUserInvitations = (useRealtime: boolean = false) => {
  // If useRealtime is true, use the real-time hook instead
  if (useRealtime) {
    // Import the hook dynamically to avoid circular dependencies
    const { useRealtimeUserInvitations } = require("./invitation.realtime.hooks")
    return useRealtimeUserInvitations()
  }

  // Otherwise use the regular store
  const userData = useUserData()
  const { invitations, loading, error, fetchUserInvitations } = useInvitationStore()

  useEffect(() => {
    if (userData?.email) {
      fetchUserInvitations(userData.email)
    }

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [userData, fetchUserInvitations])

  return { invitations, loading, error }
}

/**
 * Hook to get a specific invitation
 */
export const useInvitation = (invitationId: string, useRealtime: boolean = false) => {
  // If useRealtime is true, use the real-time hook instead
  if (useRealtime) {
    // Import the hook dynamically to avoid circular dependencies
    const { useRealtimeInvitation } = require("./invitation.realtime.hooks")
    return useRealtimeInvitation(invitationId)
  }

  // Otherwise use the regular store
  const { currentInvitation, loading, error, fetchInvitation } = useInvitationStore()

  useEffect(() => {
    if (invitationId) {
      fetchInvitation(invitationId)
    }

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [invitationId, fetchInvitation])

  return { invitation: currentInvitation, loading, error }
}

/**
 * Hook to create an invitation
 */
export const useCreateInvitation = () => {
  const [creating, setCreating] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { createInvitation } = useInvitationStore()

  const create = async (invitationData: InvitationCreateData) => {
    try {
      setCreating(true)
      setError(null)
      const invitationId = await createInvitation(invitationData)
      setCreating(false)
      return invitationId
    } catch (err) {
      setError(err as Error)
      setCreating(false)
      return null
    }
  }

  return { create, creating, error }
}

/**
 * Hook to respond to an invitation
 */
export const useRespondToInvitation = () => {
  const user = useUser()
  const [responding, setResponding] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { updateInvitationStatus } = useInvitationStore()

  const respond = async (invitationId: string, status: InvitationStatus) => {
    if (!user?.uid && status === "accepted") {
      setError(new Error("User not authenticated"))
      return false
    }

    try {
      setResponding(true)
      setError(null)
      const success = await updateInvitationStatus(invitationId, status, user?.uid)
      setResponding(false)
      return success
    } catch (err) {
      setError(err as Error)
      setResponding(false)
      return false
    }
  }

  return { respond, responding, error }
}

/**
 * Hook to delete an invitation
 */
export const useDeleteInvitation = () => {
  const [deleting, setDeleting] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { deleteInvitation } = useInvitationStore()

  const remove = async (invitationId: string) => {
    try {
      setDeleting(true)
      setError(null)
      const success = await deleteInvitation(invitationId)
      setDeleting(false)
      return success
    } catch (err) {
      setError(err as Error)
      setDeleting(false)
      return false
    }
  }

  return { remove, deleting, error }
}

/**
 * Hook to resend an invitation
 */
export const useResendInvitation = () => {
  const [resending, setResending] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { resendInvitation } = useInvitationStore()

  const resend = async (invitationId: string) => {
    try {
      setResending(true)
      setError(null)
      const success = await resendInvitation(invitationId)
      setResending(false)
      return success
    } catch (err) {
      setError(err as Error)
      setResending(false)
      return false
    }
  }

  return { resend, resending, error }
}

/**
 * Hook to invite a user to a squad
 */
export const useInviteUserToSquad = () => {
  const user = useUser()
  const [inviting, setInviting] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { createInvitation } = useInvitationStore()

  const invite = async (squadId: string, email: string, squadName: string) => {
    if (!user) {
      setError(new Error("User not authenticated"))
      return { success: false, error: "User not authenticated" }
    }

    try {
      setInviting(true)
      setError(null)

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        setError(new Error("Invalid email format"))
        setInviting(false)
        return { success: false, error: "Invalid email format" }
      }

      // Check if trying to invite yourself
      if (email.toLowerCase() === user.email?.toLowerCase()) {
        setError(new Error("You cannot invite yourself to the squad"))
        setInviting(false)
        return { success: false, error: "You cannot invite yourself to the squad" }
      }

      // Get the current squad
      const squad = await SquadService.getSquad(squadId)
      if (!squad) {
        setError(new Error("Squad not found"))
        setInviting(false)
        return { success: false, error: "Squad not found" }
      }

      // Find if the email is an existing user
      const invitee = await UserService.getUserByEmail(email)

      // Check if there's already a pending invitation for this email
      const { invitations: existingInvitations } = useInvitationStore.getState()
      const pendingInvitation = existingInvitations.find(
        (inv: Invitation) =>
          inv.inviteeEmail.toLowerCase() === email.toLowerCase() && inv.status === "pending"
      )

      if (pendingInvitation) {
        setError(new Error("This email already has a pending invitation"))
        setInviting(false)
        return { success: false, error: "This email already has a pending invitation" }
      }

      // Create invitation data
      const invitationData: InvitationCreateData = {
        squadId,
        squadName,
        inviterId: user.uid,
        inviterName: user.displayName || user.email || "",
        inviteeId: invitee ? invitee.uid : "",
        inviteeEmail: email,
      }

      // Create the invitation
      const invitationId = await createInvitation(invitationData)

      if (!invitationId) {
        setError(new Error("Failed to create invitation"))
        setInviting(false)
        return { success: false, error: "Failed to create invitation" }
      }

      // Create invitation object for email
      const invitation: Invitation = {
        id: invitationId,
        ...invitationData,
        status: "pending",
        createdAt: { toDate: () => new Date() } as any,
      }

      // Send invitation email
      const emailResult = await sendInvitationEmail(
        invitation,
        generateInvitationLink(invitationId)
      )

      setInviting(false)

      if (!emailResult.success) {
        console.warn("Invitation created but email failed to send:", emailResult.error)
      }

      return {
        success: true,
        invitationId,
        user: invitee,
        emailSent: emailResult.success,
      }
    } catch (err) {
      console.error("Error inviting user to squad:", err)
      setError(err as Error)
      setInviting(false)
      return { success: false, error: (err as Error).message }
    }
  }

  return { invite, inviting, error }
}

// ===== NEW INVITATION LINK HOOKS =====

/**
 * Hook to get or create an invitation link for a squad
 */
export const useInvitationLink = (squadId: string, useRealtime: boolean = false) => {
  const user = useUser()
  const [invitationLink, setInvitationLink] = useState<InvitationLink | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Memoize dependencies to prevent infinite re-renders
  const memoizedSquadId = useMemo(() => squadId, [squadId])

  useEffect(() => {
    if (!memoizedSquadId || !user) {
      setInvitationLink(null)
      setLoading(false)
      return () => {}
    }

    const fetchInvitationLink = async () => {
      try {
        setLoading(true)
        setError(null)

        const activeLink = await InvitationService.getActiveInvitationLink(memoizedSquadId)
        setInvitationLink(activeLink)
        setLoading(false)
      } catch (err) {
        console.error("Error fetching invitation link:", err)
        setError(err as Error)
        setLoading(false)
      }
    }

    if (useRealtime && invitationLink?.id) {
      // Use real-time subscription if we have an invitation link ID
      const { useRealtimeInvitationLink } = require("./invitation.realtime.hooks")
      return useRealtimeInvitationLink(invitationLink.id)
    } else {
      // Otherwise fetch once
      fetchInvitationLink()
    }

    return () => {}
  }, [memoizedSquadId, user, useRealtime, invitationLink?.id])

  return { invitationLink, loading, error }
}

/**
 * Hook to generate a new invitation link
 */
export const useGenerateInvitationLink = () => {
  const user = useUser()
  const [generating, setGenerating] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const generate = async (squadId: string, squadName: string) => {
    if (!user) {
      setError(new Error("User not authenticated"))
      return null
    }

    try {
      setGenerating(true)
      setError(null)

      // Check if there's already an active invitation link
      const existingLink = await InvitationService.getActiveInvitationLink(squadId)
      if (existingLink) {
        setGenerating(false)
        return existingLink
      }

      // Create new invitation link
      const invitationLinkData: InvitationLinkCreateData = {
        squadId,
        squadName,
        inviterId: user.uid,
        inviterName: user.displayName || user.email || "",
      }

      const invitationLinkId = await InvitationService.createInvitationLink(invitationLinkData)
      const newInvitationLink = await InvitationService.getInvitationLink(invitationLinkId)

      setGenerating(false)
      return newInvitationLink
    } catch (err) {
      console.error("Error generating invitation link:", err)
      setError(err as Error)
      setGenerating(false)
      return null
    }
  }

  return { generate, generating, error }
}

/**
 * Hook to send individual email invitations
 */
export const useSendInvitationEmails = () => {
  const user = useUser()
  const [sending, setSending] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const sendEmails = async (squadId: string, invitationId: string, emails: string[]) => {
    if (!user) {
      setError(new Error("User not authenticated"))
      return { success: false, error: "User not authenticated" }
    }

    try {
      setSending(true)
      setError(null)

      const results = []

      for (const email of emails) {
        // Check if email has already been sent
        const alreadySent = await InvitationSendService.hasEmailBeenSent(
          squadId,
          invitationId,
          email
        )

        if (alreadySent) {
          results.push({ email, success: false, error: "Email already sent" })
          continue
        }

        // Create invitation send record
        const invitationSendData: InvitationSendCreateData = {
          invitationId,
          email: email.toLowerCase(),
        }

        try {
          const sendId = await InvitationSendService.createInvitationSend(
            squadId,
            invitationSendData
          )

          // Send the actual email
          const invitationLink = generateInvitationLink(invitationId)
          const emailResult = await sendInvitationEmail(
            {
              id: invitationId,
              inviteeEmail: email,
              // Add other required fields for email template
            } as any,
            invitationLink
          )

          results.push({
            email,
            success: emailResult.success,
            error: emailResult.error,
            sendId,
          })
        } catch (err) {
          results.push({
            email,
            success: false,
            error: (err as Error).message,
          })
        }
      }

      setSending(false)
      return { success: true, results }
    } catch (err) {
      console.error("Error sending invitation emails:", err)
      setError(err as Error)
      setSending(false)
      return { success: false, error: (err as Error).message }
    }
  }

  return { sendEmails, sending, error }
}

/**
 * Hook to get invitation sends for a squad
 */
export const useInvitationSends = (
  squadId: string,
  invitationId?: string,
  useRealtime: boolean = false
) => {
  // If useRealtime is true, use the real-time hook instead
  if (useRealtime) {
    if (invitationId) {
      const { useRealtimeInvitationSends } = require("./invitation.realtime.hooks")
      return useRealtimeInvitationSends(squadId, invitationId)
    } else {
      const { useRealtimeSquadInvitationSends } = require("./invitation.realtime.hooks")
      return useRealtimeSquadInvitationSends(squadId)
    }
  }

  // Otherwise use regular fetch
  const [invitationSends, setInvitationSends] = useState<InvitationSend[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Memoize dependencies to prevent infinite re-renders
  const memoizedDependencies = useMemo(() => ({ squadId, invitationId }), [squadId, invitationId])

  useEffect(() => {
    if (!memoizedDependencies.squadId) {
      setInvitationSends([])
      setLoading(false)
      return () => {}
    }

    const fetchInvitationSends = async () => {
      try {
        setLoading(true)
        setError(null)

        let sends: InvitationSend[]
        if (memoizedDependencies.invitationId) {
          sends = await InvitationSendService.getInvitationSends(
            memoizedDependencies.squadId,
            memoizedDependencies.invitationId
          )
        } else {
          sends = await InvitationSendService.getSquadInvitationSends(memoizedDependencies.squadId)
        }

        setInvitationSends(sends)
        setLoading(false)
      } catch (err) {
        console.error("Error fetching invitation sends:", err)
        setError(err as Error)
        setLoading(false)
      }
    }

    fetchInvitationSends()

    return () => {}
  }, [memoizedDependencies])

  return { invitationSends, loading, error }
}
