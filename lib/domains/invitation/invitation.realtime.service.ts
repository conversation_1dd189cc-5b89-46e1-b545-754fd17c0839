import { BaseRealtimeService } from "../base/base.realtime.service"
import { Invitation, InvitationLink, InvitationSend } from "./invitation.types"

/**
 * Invitation real-time service for Firebase real-time operations
 */
export class InvitationRealtimeService {
  private static readonly COLLECTION = "invitations"

  /**
   * Subscribe to an invitation by ID
   * @param invitationId Invitation ID
   * @param callback Callback function to handle invitation changes
   * @returns Unsubscribe function
   */
  static subscribeToInvitation(
    invitationId: string,
    callback: (invitation: Invitation | null, error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToDocument<Invitation>(
      this.COLLECTION,
      invitationId,
      callback
    )
  }

  /**
   * Subscribe to an invitation link by ID
   * @param invitationId Invitation link ID
   * @param callback Callback function to handle invitation link changes
   * @returns Unsubscribe function
   */
  static subscribeToInvitationLink(
    invitationId: string,
    callback: (invitationLink: InvitationLink | null, error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToDocument<InvitationLink>(
      this.COLLECTION,
      invitationId,
      callback
    )
  }

  /**
   * Subscribe to invitations for a squad
   * @param squadId Squad ID
   * @param callback Callback function to handle invitations changes
   * @returns Unsubscribe function
   */
  static subscribeToSquadInvitations(
    squadId: string,
    callback: (invitations: Invitation[], error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToQuery<Invitation>(
      this.COLLECTION,
      [["squadId", "==", squadId]],
      callback
    )
  }

  /**
   * Subscribe to invitations for a user by email
   * @param email User email
   * @param callback Callback function to handle invitations changes
   * @returns Unsubscribe function
   */
  static subscribeToUserInvitations(
    email: string,
    callback: (invitations: Invitation[], error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToQuery<Invitation>(
      this.COLLECTION,
      [["inviteeEmail", "==", email]],
      callback
    )
  }

  /**
   * Subscribe to invitation sends for a squad
   * @param squadId Squad ID
   * @param callback Callback function to handle invitation sends changes
   * @returns Unsubscribe function
   */
  static subscribeToSquadInvitationSends(
    squadId: string,
    callback: (invitationSends: InvitationSend[], error?: Error) => void
  ): () => void {
    const collectionPath = `squads/${squadId}/invitation-sends`
    return BaseRealtimeService.subscribeToQuery<InvitationSend>(collectionPath, [], callback)
  }

  /**
   * Subscribe to invitation sends for a specific invitation
   * @param squadId Squad ID
   * @param invitationId Invitation ID
   * @param callback Callback function to handle invitation sends changes
   * @returns Unsubscribe function
   */
  static subscribeToInvitationSends(
    squadId: string,
    invitationId: string,
    callback: (invitationSends: InvitationSend[], error?: Error) => void
  ): () => void {
    const collectionPath = `squads/${squadId}/invitation-sends`
    return BaseRealtimeService.subscribeToQuery<InvitationSend>(
      collectionPath,
      [["invitationId", "==", invitationId]],
      callback
    )
  }
}
