import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Invitation status type
 */
export type InvitationStatus = "pending" | "accepted" | "rejected"

/**
 * Invitation send status type
 */
export type InvitationSendStatus = "sent" | "opened" | "joined"

/**
 * Legacy invitation entity (for backward compatibility)
 */
export interface Invitation extends BaseEntity {
  squadId: string
  squadName: string
  inviterId: string
  inviterName: string
  inviteeId: string
  inviteeEmail: string
  status: InvitationStatus
  lastUpdated?: Timestamp | null
  lastResent?: Timestamp | null
}

/**
 * New invitation link entity
 */
export interface InvitationLink extends BaseEntity {
  squadId: string
  squadName: string
  inviterId: string
  inviterName: string
  expiresAt: Timestamp
  isActive: boolean
}

/**
 * Individual invitation send entity
 */
export interface InvitationSend extends BaseEntity {
  invitationId: string
  email: string
  sentAt: Timestamp
  status: InvitationSendStatus
}

/**
 * Invitation creation data
 */
export type InvitationCreateData = Omit<Invitation, "id" | "createdAt" | "status"> & {
  status?: InvitationStatus
}

/**
 * Invitation link creation data
 */
export type InvitationLinkCreateData = Omit<InvitationLink, "id" | "createdAt" | "expiresAt" | "isActive">

/**
 * Invitation send creation data
 */
export type InvitationSendCreateData = Omit<InvitationSend, "id" | "createdAt" | "sentAt" | "status">

/**
 * Invitation update data
 */
export type InvitationUpdateData = Partial<Invitation>

/**
 * Invitation link update data
 */
export type InvitationLinkUpdateData = Partial<InvitationLink>

/**
 * Invitation send update data
 */
export type InvitationSendUpdateData = Partial<InvitationSend>
