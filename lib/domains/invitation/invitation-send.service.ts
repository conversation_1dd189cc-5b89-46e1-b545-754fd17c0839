import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  setDoc,
  deleteDoc,
} from "firebase/firestore"
import { ServiceResponse } from "../base/base.types"
import {
  InvitationSend,
  InvitationSendCreateData,
  InvitationSendUpdateData,
  InvitationSendStatus,
} from "./invitation.types"

/**
 * Invitation send service for Firebase operations
 */
export class InvitationSendService {
  /**
   * Get collection path for invitation sends
   * @param squadId Squad ID
   * @returns Collection path
   */
  private static getCollectionPath(squadId: string): string {
    return `squads/${squadId}/invitation-sends`
  }

  /**
   * Create a new invitation send
   * @param squadId Squad ID
   * @param invitationSendData Invitation send data
   * @returns The new invitation send ID
   */
  static async createInvitationSend(
    squadId: string,
    invitationSendData: InvitationSendCreateData
  ): Promise<string> {
    try {
      const collectionPath = this.getCollectionPath(squadId)
      const invitationSendRef = doc(collection(db, collectionPath))
      const invitationSendId = invitationSendRef.id

      await setDoc(invitationSendRef, {
        ...invitationSendData,
        id: invitationSendId,
        sentAt: serverTimestamp(),
        status: "sent" as InvitationSendStatus,
        createdAt: serverTimestamp(),
      })

      return invitationSendId
    } catch (error) {
      console.error("Error creating invitation send:", error)
      throw error
    }
  }

  /**
   * Get an invitation send by ID
   * @param squadId Squad ID
   * @param invitationSendId Invitation send ID
   * @returns The invitation send or null if not found
   */
  static async getInvitationSend(
    squadId: string,
    invitationSendId: string
  ): Promise<InvitationSend | null> {
    try {
      const collectionPath = this.getCollectionPath(squadId)
      const invitationSendDoc = await getDoc(doc(db, collectionPath, invitationSendId))
      if (invitationSendDoc.exists()) {
        return { ...invitationSendDoc.data(), id: invitationSendId } as InvitationSend
      }
      return null
    } catch (error) {
      console.error("Error getting invitation send:", error)
      throw error
    }
  }

  /**
   * Get all invitation sends for an invitation
   * @param squadId Squad ID
   * @param invitationId Invitation ID
   * @returns Array of invitation sends
   */
  static async getInvitationSends(squadId: string, invitationId: string): Promise<InvitationSend[]> {
    try {
      const collectionPath = this.getCollectionPath(squadId)
      const invitationSendsRef = collection(db, collectionPath)
      const q = query(invitationSendsRef, where("invitationId", "==", invitationId))
      const querySnapshot = await getDocs(q)

      return querySnapshot.docs.map(
        (doc) => ({ ...doc.data(), id: doc.id }) as InvitationSend
      )
    } catch (error) {
      console.error("Error getting invitation sends:", error)
      throw error
    }
  }

  /**
   * Get all invitation sends for a squad
   * @param squadId Squad ID
   * @returns Array of invitation sends
   */
  static async getSquadInvitationSends(squadId: string): Promise<InvitationSend[]> {
    try {
      const collectionPath = this.getCollectionPath(squadId)
      const invitationSendsRef = collection(db, collectionPath)
      const querySnapshot = await getDocs(invitationSendsRef)

      return querySnapshot.docs.map(
        (doc) => ({ ...doc.data(), id: doc.id }) as InvitationSend
      )
    } catch (error) {
      console.error("Error getting squad invitation sends:", error)
      throw error
    }
  }

  /**
   * Update an invitation send
   * @param squadId Squad ID
   * @param invitationSendId Invitation send ID
   * @param invitationSendData Invitation send data to update
   * @returns Service response
   */
  static async updateInvitationSend(
    squadId: string,
    invitationSendId: string,
    invitationSendData: InvitationSendUpdateData
  ): Promise<ServiceResponse> {
    try {
      const collectionPath = this.getCollectionPath(squadId)
      await updateDoc(doc(db, collectionPath, invitationSendId), {
        ...invitationSendData,
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating invitation send:", error)
      return { success: false, error }
    }
  }

  /**
   * Update invitation send status
   * @param squadId Squad ID
   * @param invitationSendId Invitation send ID
   * @param status New status
   * @returns Service response
   */
  static async updateInvitationSendStatus(
    squadId: string,
    invitationSendId: string,
    status: InvitationSendStatus
  ): Promise<ServiceResponse> {
    try {
      const collectionPath = this.getCollectionPath(squadId)
      await updateDoc(doc(db, collectionPath, invitationSendId), {
        status,
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating invitation send status:", error)
      return { success: false, error }
    }
  }

  /**
   * Delete an invitation send
   * @param squadId Squad ID
   * @param invitationSendId Invitation send ID
   * @returns Service response
   */
  static async deleteInvitationSend(
    squadId: string,
    invitationSendId: string
  ): Promise<ServiceResponse> {
    try {
      const collectionPath = this.getCollectionPath(squadId)
      await deleteDoc(doc(db, collectionPath, invitationSendId))
      return { success: true }
    } catch (error) {
      console.error("Error deleting invitation send:", error)
      return { success: false, error }
    }
  }

  /**
   * Hide invitation sends when main invitation expires
   * @param squadId Squad ID
   * @param invitationId Invitation ID
   * @returns Service response
   */
  static async hideExpiredInvitationSends(
    squadId: string,
    invitationId: string
  ): Promise<ServiceResponse> {
    try {
      const invitationSends = await this.getInvitationSends(squadId, invitationId)

      // Update all sends to mark them as hidden/expired
      const updatePromises = invitationSends.map((send) =>
        this.updateInvitationSend(squadId, send.id, { status: "sent" })
      )

      await Promise.all(updatePromises)

      return { success: true }
    } catch (error) {
      console.error("Error hiding expired invitation sends:", error)
      return { success: false, error }
    }
  }

  /**
   * Check if email has already been sent for an invitation
   * @param squadId Squad ID
   * @param invitationId Invitation ID
   * @param email Email to check
   * @returns True if email has been sent
   */
  static async hasEmailBeenSent(
    squadId: string,
    invitationId: string,
    email: string
  ): Promise<boolean> {
    try {
      const collectionPath = this.getCollectionPath(squadId)
      const invitationSendsRef = collection(db, collectionPath)
      const q = query(
        invitationSendsRef,
        where("invitationId", "==", invitationId),
        where("email", "==", email.toLowerCase())
      )
      const querySnapshot = await getDocs(q)

      return !querySnapshot.empty
    } catch (error) {
      console.error("Error checking if email has been sent:", error)
      return false
    }
  }
}
